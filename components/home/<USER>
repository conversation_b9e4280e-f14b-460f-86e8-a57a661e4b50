"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Play } from "lucide-react"
import { useEffect, useRef, useState } from "react"

// Hero chính xác theo ảnh 1
export default function HeroSection() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [videoLoaded, setVideoLoaded] = useState(false)
  const [videoError, setVideoError] = useState(false)

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadedData = () => {
      setVideoLoaded(true)
      // Đảm bảo video được phát ngay khi load xong
      video.play().catch((error) => {
        console.error("Video autoplay failed:", error)
        setVideoError(true)
      })
    }

    const handleError = () => {
      console.error("Video failed to load")
      setVideoError(true)
    }

    const handleCanPlay = () => {
      // Thử phát video khi có thể phát
      video.play().catch((error) => {
        console.error("Video play failed:", error)
      })
    }

    video.addEventListener('loadeddata', handleLoadedData)
    video.addEventListener('error', handleError)
    video.addEventListener('canplay', handleCanPlay)

    // Thử phát video ngay lập tức
    if (video.readyState >= 3) {
      handleLoadedData()
    }

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData)
      video.removeEventListener('error', handleError)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [])

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Video Background chính xác */}
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover"
        autoPlay
        muted
        loop
        playsInline
        preload="auto"
        onLoadedData={() => setVideoLoaded(true)}
        onError={() => setVideoError(true)}
      >
        <source src="https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2Fvideos%2Fatto-vid-bg-desktop.mp4?alt=media&token=d296b363-be22-4e8b-9ce3-886fa8618a44" type="video/mp4" />
        <source src="/placeholder-video.mp4" type="video/mp4" />
      </video>

      {/* Fallback background nếu video không load được */}
      {videoError && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800" />
      )}

      {/* Loading indicator */}
      {!videoLoaded && !videoError && (
        <div className="absolute inset-0 bg-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600"></div>
        </div>
      )}

      {/* Overlay chính xác */}
      <div className="absolute inset-0 bg-black/40" />

      {/* Content theo ảnh thiết kế - text overlay trên video */}
      <div className="relative z-10 text-left max-w-7xl mx-auto px-6 h-full flex items-center">
        <div className="max-w-2xl">
          {/* Badge/Tag như trong ảnh */}
          <div className="inline-block bg-orange-600/20 backdrop-blur-sm border border-orange-600/30 text-orange-400 px-4 py-2 rounded-full text-sm font-medium mb-6">
            Electric Vehicle Pioneer
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 leading-tight text-white">
            It's More Than Just a Car
          </h1>
          <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-lg">
            Experience the future of mobility with our revolutionary electric vehicles designed for the modern world.
          </p>

          {/* Buttons theo thiết kế */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 text-base font-medium">
              Explore Models
            </Button>
            <Button
              size="lg"
              variant="ghost"
              className="text-white hover:bg-white/10 px-8 py-3 text-base font-medium border border-white/30"
            >
              <Play className="mr-2 h-4 w-4" />
              Watch Video
            </Button>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}
