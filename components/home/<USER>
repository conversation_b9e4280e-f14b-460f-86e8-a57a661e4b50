"use client"

import { Button } from "@/components/ui/button"
import Image from "next/image"

export default function ModelsShowcase() {
  const models = [
    {
      name: "BYD ATTO 3",
      subtitle: "Compact SUV",
      description: "The perfect blend of style, technology, and sustainability for urban adventures.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2FAtto%20Interior%2Fatto-2025%2FUntitled%20(4)%20(1).webp?alt=media&token=fc29831d-49d7-4666-afba-20c0fa2c055e",
      price: "Starting from $45,000",
      features: ["420km Range", "Fast Charging", "Smart Interior"],
    },
    {
      name: "BYD HAN",
      subtitle: "Executive Sedan",
      description: "Sophisticated design with cutting-edge electric technology and luxury comfort.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2FM6%2F%E4%BA%9A%E5%A4%AA%E5%AE%8BMax%20KV%E8%B7%AF%E8%B7%91%E7%AF%87%20(1).webp?alt=media&token=9b53c1f7-2991-4c07-99f2-ee47b424dfde",
      price: "Starting from $65,000",
      features: ["605km Range", "Luxury Interior", "Dragon Face Design"],
    },
    {
      name: "BYD TANG",
      subtitle: "7-Seater SUV",
      description: "Spacious family SUV with all-wheel drivhe and premium comfort features.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2Fsealion%2Fgeneral%2F20240918%E4%BA%9A%E5%A4%AA%E6%B5%B7%E7%8B%AE07%E5%8D%95%E8%BD%A6%E8%B7%AF%E8%B7%91KV.webp?alt=media&token=cddc34a4-b19d-4bef-ae3a-45dad85afffc",
      price: "Starting from $55,000",
      features: ["505km Range", "All-Wheel Drive", "7 Seats"],
    },
    {
      name: "BYD SEAL",
      subtitle: "Sports Sedan",
      description: "Dynamic performance meets elegant design in this premium electric sedan.",
      image: "https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2FSeal%2Fkv%2Fbyd%20%E8%B5%9B%E9%81%93kv%2099%20RGB%20x.webp?alt=media&token=309710af-74f9-4e79-a31e-8fd4599fe83c",
      price: "Starting from $50,000",
      features: ["550km Range", "Sports Performance", "Premium Design"],
    },
    {
      name: "BYD DOLPHIN",
      subtitle: "Compact Hatchback",
      description: "Agile, efficient, and perfect for city driving with smart technology.",
      image: "",
      price: "Starting from $35,000",
      features: ["405km Range", "City Driving", "Smart Tech"],
    },
  ]

  return (
    <>
      {models.map((model, index) => (
        <section key={index} className="relative h-screen flex items-center justify-center overflow-hidden">
          {/* Background Image */}
          <div className="absolute inset-0">
            <Image
              src={model.image}
              alt={model.name}
              fill
              className="object-cover"
              onError={(e) => {
                e.currentTarget.src = "/placeholder-car.jpg"
              }}
            />
          </div>

          {/* Dark Overlay */}
          <div className="absolute inset-0 bg-black/60" />

          {/* Content overlay theo thiết kế trong ảnh */}
          <div className="relative z-10 text-center max-w-4xl mx-auto px-6">
            {/* Model badge */}
            <div className="inline-block bg-orange-600/20 backdrop-blur-sm border border-orange-600/30 text-orange-400 px-6 py-2 rounded-full text-sm font-medium mb-6">
              {model.subtitle}
            </div>

            <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              {model.name}
            </h2>

            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-2xl mx-auto leading-relaxed">
              {model.description}
            </p>

            {/* Features list */}
            <div className="flex flex-wrap justify-center gap-6 mb-8">
              {model.features.map((feature, idx) => (
                <div key={idx} className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-orange-600 rounded-full mr-3"></div>
                  <span className="text-lg">{feature}</span>
                </div>
              ))}
            </div>

            {/* Price */}
            <div className="text-2xl md:text-3xl font-bold text-orange-600 mb-8">
              {model.price}
            </div>

            {/* Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-orange-600 hover:bg-orange-700 text-white px-10 py-4 text-lg font-medium">
                Learn More
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-black px-10 py-4 text-lg font-medium bg-transparent"
              >
                Test Drive
              </Button>
            </div>
          </div>
        </section>
      ))}
    </>
  )
}
