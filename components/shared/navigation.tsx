"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { useState } from "react"
import { <PERSON>u, X } from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-white/10">
      <div className="container mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          {/* Logo chính xác như ảnh */}
          <Link href="/" className="text-white dark:text-white">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white dark:bg-white rounded-sm flex items-center justify-center">
                <span className="text-black font-bold text-sm">BYD</span>
              </div>
              <span className="text-sm font-medium">Build Your Dreams</span>
            </div>
          </Link>

          {/* Desktop Menu - ch<PERSON>h xác nh<PERSON> */}
          <div className="hidden lg:flex items-center space-x-8">
            <Link href="#" className="text-white dark:text-white text-sm hover:text-orange-500 transition-colors">
              Models
            </Link>
            <Link href="#" className="text-white dark:text-white text-sm hover:text-orange-500 transition-colors">
              Technology
            </Link>
            <Link href="#" className="text-white dark:text-white text-sm hover:text-orange-500 transition-colors">
              Innovation
            </Link>
            <Link href="#" className="text-white dark:text-white text-sm hover:text-orange-500 transition-colors">
              About
            </Link>
            <Link href="#" className="text-white dark:text-white text-sm hover:text-orange-500 transition-colors">
              Contact
            </Link>
          </div>

          {/* Right side buttons */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle />

            {/* CTA Button chính xác như ảnh */}
            <Button className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 text-sm font-medium rounded-md">
              Test Drive
            </Button>

            {/* Mobile Menu Button */}
            <button className="lg:hidden text-white dark:text-white" onClick={() => setIsOpen(!isOpen)}>
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>
    </nav>
  )
}
