import { Facebook, Twitter, Instagram, Youtube } from "lucide-react"

export default function Footer() {
  const footerSections = [
    {
      title: "Models",
      links: ["BYD ATTO 3", "BYD HAN", "BYD TANG", "BYD SEAL", "BYD DOLPHIN", "All Models"],
    },
    {
      title: "Services",
      links: ["Test Drive", "Service Centers", "Warranty", "Insurance", "Financing"],
    },
    {
      title: "Company",
      links: ["About BYD", "Careers", "News", "Investors", "Sustainability"],
    },
    {
      title: "Support",
      links: ["Contact Us", "FAQ", "Owner's Manual", "Recalls", "Customer Care"],
    },
  ]

  return (
    <footer className="bg-black text-white py-16">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-white rounded-sm flex items-center justify-center">
                <span className="text-black font-bold text-lg">BYD</span>
              </div>
              <span className="text-lg font-medium">Build Your Dreams</span>
            </div>
            <p className="text-gray-400 mb-6">
              Building Your Dreams with sustainable electric mobility solutions for a better tomorrow.
            </p>
            <div className="flex space-x-4">
              <Facebook className="h-6 w-6 text-gray-400 hover:text-orange-500 cursor-pointer transition-colors" />
              <Twitter className="h-6 w-6 text-gray-400 hover:text-orange-500 cursor-pointer transition-colors" />
              <Instagram className="h-6 w-6 text-gray-400 hover:text-orange-500 cursor-pointer transition-colors" />
              <Youtube className="h-6 w-6 text-gray-400 hover:text-orange-500 cursor-pointer transition-colors" />
            </div>
          </div>

          {footerSections.map((section, index) => (
            <div key={index}>
              <h3 className="font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a href="#" className="text-gray-400 hover:text-orange-500 transition-colors">
                      {link}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2024 BYD Company Limited. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-orange-500 text-sm transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-orange-500 text-sm transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-gray-400 hover:text-orange-500 text-sm transition-colors">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
