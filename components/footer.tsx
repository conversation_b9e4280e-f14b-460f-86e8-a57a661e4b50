import { Facebook, Instagram } from "lucide-react"
import Image from "next/image"

export default function Footer() {
  const sitemapLinks = [
    "Home",
    "BYD Sealion 6 DM-i",
    "DENZA D9",
    "BYD ATTO 3",
    "BYD M6",
    "BYD SEAL",
    "BYD Dolphin",
    "Trade-In",
    "Voucher Enquiry",
    "Careers",
    "Privacy Policy",
    "Terms & Condition"
  ]

  const locations = [
    {
      name: "BYD by 1826 | Suntec",
      address: "No.3 Temasek Boulevard\n#01-463 Suntec City Mall\nSingapore 038983",
      phone: null
    },
    {
      name: "BYD by 1826 | Boat Quay",
      address: "Level 1 - 3, 30/34 Boat Quay\nSingapore 049822",
      phone: "+65 6970 6778"
    },
    {
      name: "BYD by 1826 | Zhongshan Park",
      address: "1 Jalan Rajah, Zhongshan Park\n#01-02 & 03, Singapore 329133",
      phone: "+65 8031 5135"
    },
    {
      name: "BYD by 1826 | Tanjong Pagar",
      address: "Guoco Tower, 5 Wallich St\n#01-17/18, Singapore 078883",
      phone: "+65 8031 3612"
    },
    {
      name: "BYD by 1826 | Waterway Point",
      address: "83 Punggol Central, Waterway Point\n#01-12 Singapore 828761",
      phone: null
    },
    {
      name: "BYD by 1826 | The Centrepoint",
      address: "176 Orchard Rd, The Centrepoint\n#B2-01, Singapore 238843",
      phone: null
    },
    {
      name: "BYD by 1826 | IMM",
      address: null,
      phone: null
    }
  ]

  return (
    <footer className="bg-black text-white py-16">
      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-4 gap-12 mb-12">

          {/* BYD Logo Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-8">
              <div className="text-3xl font-bold mr-4">BYD</div>
              <div className="bg-white text-black px-3 py-1 text-sm font-medium">
                by 1826
              </div>
            </div>
          </div>

          {/* SITEMAP Section */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-white">SITEMAP</h3>
            <ul className="space-y-3">
              {sitemapLinks.map((link, index) => (
                <li key={index}>
                  <a href="#" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* OUR LOCATIONS Section */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-white">OUR LOCATIONS</h3>
            <div className="space-y-6">
              {locations.map((location, index) => (
                <div key={index} className="text-sm">
                  <h4 className="font-medium text-white mb-2">{location.name}</h4>
                  {location.address && (
                    <p className="text-gray-300 mb-1 whitespace-pre-line">
                      {location.address}
                    </p>
                  )}
                  {location.phone && (
                    <p className="text-gray-300">{location.phone}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* SOCIALS & CONTACT US Section */}
          <div>
            <h3 className="font-bold text-lg mb-6 text-white">SOCIALS</h3>
            <div className="space-y-3 mb-8">
              <a href="#" className="flex items-center text-gray-300 hover:text-orange-500 transition-colors text-sm">
                <Instagram className="h-4 w-4 mr-2" />
                bydby1826_singapore
              </a>
              <a href="#" className="flex items-center text-gray-300 hover:text-orange-500 transition-colors text-sm">
                <Facebook className="h-4 w-4 mr-2" />
                BYDby1826Singapore
              </a>
            </div>

            <h3 className="font-bold text-lg mb-6 text-white">CONTACT US</h3>
            <div className="space-y-3 text-sm">
              <p className="text-gray-300"><EMAIL></p>
              <p className="text-gray-300"><EMAIL></p>
              <p className="text-gray-300"><EMAIL></p>
            </div>
          </div>

        </div>
      </div>
    </footer>
  )
}
