"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Play, ChevronDown } from "lucide-react"
import { useEffect, useRef } from "react"

export default function HeroSection() {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(console.error)
    }
  }, [])

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Video Background */}
      <video ref={videoRef} className="absolute inset-0 w-full h-full object-cover" autoPlay muted loop playsInline>
        <source src="https://firebasestorage.googleapis.com/v0/b/sg-9d944.appspot.com/o/BYD%201826%2Fvideos%2Fatto-vid-bg-desktop.mp4?alt=media&token=d296b363-be22-4e8b-9ce3-886fa8618a44" type="video/mp4" />
      </video>

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black/50" />

      {/* Navigation */}
      <nav className="absolute top-0 left-0 right-0 z-20 p-6">
        <div className="container mx-auto flex items-center justify-between">
          <div className="text-2xl font-bold text-white">BYD</div>
          <div className="hidden md:flex items-center space-x-8">
            <a href="#" className="text-white hover:text-orange-500 transition-colors">
              Models
            </a>
            <a href="#" className="text-white hover:text-orange-500 transition-colors">
              Technology
            </a>
            <a href="#" className="text-white hover:text-orange-500 transition-colors">
              About
            </a>
            <a href="#" className="text-white hover:text-orange-500 transition-colors">
              Contact
            </a>
          </div>
          <Button className="bg-orange-600 hover:bg-orange-700">Test Drive</Button>
        </div>
      </nav>

      {/* Hero Content */}
      <div className="relative z-10 text-center max-w-4xl mx-auto px-6">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">It's More Than Just a Car</h1>
        <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl mx-auto">
          Experience the future of mobility with our revolutionary electric vehicles designed for the modern world.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-orange-600 hover:bg-orange-700 text-lg px-8 py-4">
            Explore Models
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="border-white text-white hover:bg-white hover:text-black text-lg px-8 py-4 bg-transparent"
          >
            <Play className="mr-2 h-5 w-5" />
            Watch Video
          </Button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <ChevronDown className="h-8 w-8 text-white animate-bounce" />
      </div>
    </section>
  )
}
